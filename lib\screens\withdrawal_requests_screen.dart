import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
// import 'package:share_plus/share_plus.dart';
import '../services/api_service.dart';

class WithdrawalRequestsScreen extends StatefulWidget {
  const WithdrawalRequestsScreen({Key? key}) : super(key: key);

  @override
  State<WithdrawalRequestsScreen> createState() =>
      _WithdrawalRequestsScreenState();
}

class _WithdrawalRequestsScreenState extends State<WithdrawalRequestsScreen> {
  bool _isLoading = true;
  List<dynamic> _requests = [];
  String _errorMessage = '';

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      case 'pending':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  void _showBankDetailsBottomSheet(
      BuildContext context, Map<String, dynamic> request) {
    final accountNumber =
        request['bank_account_number']?.toString() ?? 'Not provided';
    final ifscCode = request['bank_ifsc_code']?.toString() ?? 'Not provided';
    final accountHolderName =
        request['bank_account_name']?.toString() ?? 'Not provided';
    final bankName = request['bank_name']?.toString() ?? 'Not provided';
    final bankBranch = request['bank_branch']?.toString() ?? 'Not provided';
    final storeReference = request['store_reference']?.toString() ?? 'N/A';
    final transactionReference =
        request['transaction_reference']?.toString() ?? 'N/A';
    final payoutAmount =
        '₹${(request['payout_amount'] ?? 0).toStringAsFixed(2)}';
    final currentBalance =
        '₹${(request['current_account_balance'] ?? 0).toStringAsFixed(2)}';

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Center(
              child: Text(
                'Bank Details',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailRow('Store Reference', storeReference),
            _buildDetailRow('Transaction Reference', transactionReference),
            _buildDetailRow('Payout Amount', payoutAmount),
            _buildDetailRow('Current Balance', currentBalance),
            const SizedBox(height: 16),
            _buildDetailRow('Bank Name', bankName),
            _buildDetailRow('Account Holder', accountHolderName),
            _buildCopyableDetailRow('Account Number', accountNumber),
            _buildCopyableDetailRow('IFSC Code', ifscCode),
            if (bankBranch != 'Not provided')
              _buildDetailRow('Branch', bankBranch),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  final shareText = '''
                  Bank Name: $bankName
                  Account Holder: $accountHolderName
                  Account Number: $accountNumber
                  IFSC Code: $ifscCode
                  ''';
                  //Share.share(shareText.trim());
                },
                child: const Text('Share Bank Details'),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Divider(),
        ],
      ),
    );
  }

  Widget _buildCopyableDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              if (value != 'Not provided')
                InkWell(
                  onTap: () {
                    Clipboard.setData(ClipboardData(text: value));
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('$label copied to clipboard')),
                    );
                  },
                  child: const Text(
                    'COPY',
                    style: TextStyle(
                      color: Colors.blue,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Divider(),
        ],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _loadRequests();
  }

  Future<void> _loadRequests() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      print('Loading withdrawal requests...');
      final apiService = Provider.of<ApiService>(context, listen: false);
      final response = await apiService.getWithdrawalRequests();

      if (mounted) {
        print('Withdrawal requests loaded: ${response.length} items');
        setState(() {
          _requests = response;
          _isLoading = false;
        });
      }
    } on DioException catch (e) {
      final errorMessage =
          e.response?.data?.toString() ?? e.message ?? 'Unknown error';
      print('Error loading withdrawal requests: $errorMessage');
      print('Error details: $e');

      if (mounted) {
        setState(() {
          _errorMessage =
              'Error: ${e.message ?? 'Failed to load withdrawal requests'}';
          if (e.response?.statusCode == 401) {
            _errorMessage = 'Session expired. Please login again.';
            // Optionally handle logout here
          }
          _isLoading = false;
        });
      }
    } catch (e, stackTrace) {
      print('Unexpected error: $e');
      print('Stack trace: $stackTrace');

      if (mounted) {
        setState(() {
          _errorMessage = 'An unexpected error occurred';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Withdrawal Requests'),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _errorMessage,
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: _loadRequests,
                        icon: const Icon(Icons.refresh),
                        label: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _requests.isEmpty
                  ? Center(
                      child: Text(
                        'No withdrawal requests found',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _requests.length,
                      itemBuilder: (context, index) {
                        final request = _requests[index];
                        final storeHandle =
                            request['storehandle']?.toString().toUpperCase() ??
                                'UNKNOWN STORE';
                        final amount =
                            '₹${(request['payout_amount'] ?? 0).toStringAsFixed(2)}';
                        final status =
                            (request['transaction_status'] ?? 'PENDING')
                                .toString()
                                .toUpperCase();
                        final transactionRef =
                            request['transaction_reference'] ?? 'N/A';
                        final requestDate =
                            request['payout_release_date'] ?? 'N/A';

                        return Card(
                          margin: const EdgeInsets.only(bottom: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          elevation: 3,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Row: Store + Status
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            storeHandle,
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            "Ref: $transactionRef",
                                            style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Chip(
                                      label: Text(
                                        status,
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 12,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      backgroundColor: _getStatusColor(status),
                                    ),
                                  ],
                                ),
                                const Divider(height: 20),
                                // Row: Amount + Date
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      amount,
                                      style: const TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.green,
                                      ),
                                    ),
                                    Text(
                                      "Requested: $requestDate",
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 12),
                                // Row: Actions
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    TextButton.icon(
                                      onPressed: () =>
                                          _showBankDetailsBottomSheet(
                                              context, request),
                                      icon: const Icon(Icons.visibility),
                                      label: const Text("Details"),
                                    ),
                                    const SizedBox(width: 8),
                                    ElevatedButton(
                                      onPressed: () => _approveRequest(
                                          request), // you define
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.green,
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 16, vertical: 8),
                                      ),
                                      child: const Text("Approve"),
                                    ),
                                    const SizedBox(width: 8),
                                    OutlinedButton(
                                      onPressed: () =>
                                          _rejectRequest(request), // you define
                                      style: OutlinedButton.styleFrom(
                                        foregroundColor: Colors.red,
                                      ),
                                      child: const Text("Reject"),
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ),
                        );
                      },
                    ),
    );
  }
}

void _approveRequest(Map<String, dynamic> request) {
  // TODO: Implement approve logic
  print("Approved: ${request['transaction_reference']}");
}

void _rejectRequest(Map<String, dynamic> request) {
  // TODO: Implement reject logic
  print("Rejected: ${request['transaction_reference']}");
}
