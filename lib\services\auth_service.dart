import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:logger/logger.dart';

class AuthService extends ChangeNotifier {
  static const String _tokenKey = 'auth_token';
  static const String _baseUrl = 'https://e2e-77-175.ssdcloudindia.net/dev';

  final Dio _dio = Dio();
  late final FlutterSecureStorage _secureStorage;
  final Logger _logger = Logger();
  String? _cachedToken;

  // Expose Dio instance for ApiService
  Dio get dio => _dio;

  AuthService() {
    _initStorage();
  }

  Future<void> _initStorage() async {
    try {
      _secureStorage = const FlutterSecureStorage(
        aOptions: AndroidOptions(encryptedSharedPreferences: true),
      );
      // Test the storage
      await _secureStorage.write(key: '__test__', value: 'test');
      await _secureStorage.delete(key: '__test__');
    } catch (e) {
      _logger.e('Error initializing secure storage: $e');
      // Fallback to in-memory storage for web or other platforms
      _secureStorage = const FlutterSecureStorage(
        aOptions: AndroidOptions(encryptedSharedPreferences: false),
      );
    }
  }

  // Get stored token
  Future<String?> getToken() async {
    try {
      // Return cached token if available
      if (_cachedToken != null) {
        _logger.i('Returning cached token (first 5 chars): ${_cachedToken!.substring(0, _cachedToken!.length > 5 ? 5 : _cachedToken!.length)}...');
        return _cachedToken;
      }
      
      // Read from secure storage
      _cachedToken = await _secureStorage.read(key: _tokenKey);
      
      if (_cachedToken != null) {
        _logger.i('Retrieved token from secure storage (first 5 chars): ${_cachedToken!.substring(0, _cachedToken!.length > 5 ? 5 : _cachedToken!.length)}...');
      } else {
        _logger.w('No token found in secure storage');
      }
      
      return _cachedToken;
    } catch (e) {
      _logger.e('Error reading token: $e');
      return null;
    }
  }

  // Send OTP to email
  Future<bool> sendOtp(String email) async {
    try {
      final response = await _dio.post(
        '$_baseUrl/swadesic_admin/send-otp/',
        data: {'email': email},
        options: Options(
          headers: {'Content-Type': 'application/json'},
        ),
      );

      if (response.statusCode == 200) {
        _logger.i('OTP sent successfully to $email');
        return true;
      }
      return false;
    } on DioException catch (e) {
      _logger.e('Error sending OTP', error: e);
      rethrow;
    }
  }

  // Verify OTP and get token
  Future<bool> verifyOtp(String email, String otp) async {
    try {
      _logger.i('Verifying OTP for $email');
      final otpNumber = int.tryParse(otp) ?? 0;
      if (otpNumber == 0) {
        _logger.e('Invalid OTP format: $otp');
        return false;
      }

      _logger.i('Sending OTP verification request...');
      final response = await _dio.post(
        '$_baseUrl/swadesic_admin/verify-otp/',
        data: {
          'email': email,
          'otp': otpNumber,
        },
        options: Options(
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          validateStatus: (status) => status! < 500,
        ),
      );

      _logger.i('OTP verification response: ${response.statusCode} - ${response.data}');
      _logger.i('Response headers: ${response.headers}');
      _logger.i('Request headers: ${response.requestOptions.headers}');

      if (response.statusCode == 200) {
        final token = response.data['token'] as String?;
        if (token != null && token.isNotEmpty) {
          try {
            await _secureStorage.write(key: _tokenKey, value: token);
            _cachedToken = token; // Update cache
            _logger.i('Token saved successfully');
            notifyListeners(); // Notify listeners about auth state change
            return true;
          } catch (e) {
            _logger.e('Error saving token: $e');
            // Even if storage fails, we can continue with the token in memory
            _cachedToken = token;
            notifyListeners();
            return true;
          }
        } else {
          _logger.e('No token received in response');
        }
      } else {
        _logger
            .e('OTP verification failed with status: ${response.statusCode}');
        _logger.e('Response data: ${response.data}');
      }
      return false;
    } on DioException catch (e) {
      _logger.e('DioException during OTP verification:', error: e);
      _logger.e('Error type: ${e.type}');
      _logger.e('Error message: ${e.message}');
      _logger.e('Response data: ${e.response?.data}');
      _logger.e('Status code: ${e.response?.statusCode}');
      rethrow;
    } catch (e, stackTrace) {
      _logger.e('Unexpected error during OTP verification:',
          error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  // Logout and clear token
  Future<void> logout() async {
    try {
      await _secureStorage.delete(key: _tokenKey);
      _logger.i('User logged out');
    } catch (e) {
      _logger.e('Error during logout', error: e);
      rethrow;
    }
  }

  // Check if user is authenticated
  Future<bool> isAuthenticated() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  // Get auth headers for API calls
  Future<Map<String, String>> getAuthHeaders() async {
    final token = await getToken();
    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
  }
}
