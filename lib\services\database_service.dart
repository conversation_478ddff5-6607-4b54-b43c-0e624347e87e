import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_dotenv/flutter_dotenv.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  
  // Public factory constructor
  factory DatabaseService() {
    return _instance;
  }
  
  // Private constructor
  DatabaseService._internal();
  
  // Getter for the singleton instance
  static DatabaseService get instance => _instance;

  // Database configuration from environment variables
  String? get _dbName => dotenv.env['DB_NAME'];
  String? get _dbUser => dotenv.env['ADMIN_DB_USER'];
  String? get _dbPassword => dotenv.env['DB_PASSWORD'];
  String? get _dbHost => dotenv.env['DB_HOST'];

  // API endpoints - replace with your actual backend URL
  // TODO: Move this to environment variables
  final String _baseUrl = 'http://localhost:3000/api';

  // Check if database credentials are available
  bool get isConfigured => 
      _dbName != null && 
      _dbUser != null && 
      _dbPassword != null && 
      _dbHost != null;

  /// Fetches all database schemas
  /// 
  /// Returns a list of schema information maps
  Future<List<Map<String, dynamic>>> getSchemas() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/schemas'),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data['schemas']);
      } else {
        throw Exception('Failed to load schemas: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error fetching schemas: $e');
    }
  }

  /// Fetches all tables for a given schema
  /// 
  /// [schema] The name of the schema to fetch tables for
  /// Returns a list of table information maps
  Future<List<Map<String, dynamic>>> getTables(String schema) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/tables?schema=$schema'),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data['tables']);
      } else {
        throw Exception('Failed to load tables: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error fetching tables: $e');
    }
  }

  /// Executes a SQL query and returns the results
  /// 
  /// [query] The SQL query to execute
  /// Returns a list of result rows as maps
  Future<List<Map<String, dynamic>>> executeQuery(String query) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/execute-query'),
        headers: {
          'Content-Type': 'application/json',
          ..._getAuthHeaders(),
        },
        body: jsonEncode({'query': query}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data['results']);
      } else {
        throw Exception('Query execution failed: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error executing query: $e');
    }
  }

  /// Fetches column information for a specific table
  /// 
  /// [schema] The schema name
  /// [table] The table name
  /// Returns a list of column information maps
  Future<List<Map<String, dynamic>>> getTableColumns(
      String schema, String table) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/table-columns?schema=$schema&table=$table'),
        headers: _getAuthHeaders(),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return List<Map<String, dynamic>>.from(data['columns']);
      } else {
        throw Exception('Failed to load table columns: ${response.body}');
      }
    } catch (e) {
      throw Exception('Error fetching table columns: $e');
    }
  }

  // Helper method to get authentication headers
  Map<String, String> _getAuthHeaders() {
    return {
      'X-DB-Name': _dbName ?? '',
      'X-DB-User': _dbUser ?? '',
      'X-DB-Password': _dbPassword ?? '',
      'X-DB-Host': _dbHost ?? '',
    };
  }
}
