import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:csv/csv.dart' show ListToCsvConverter;
import 'package:http/http.dart' as http;
import 'services/database_service.dart';

class SqlEditorPage extends StatefulWidget {
  const SqlEditorPage({super.key});

  @override
  State<SqlEditorPage> createState() => _SqlEditorPageState();
}

class _SqlEditorPageState extends State<SqlEditorPage> {
  final TextEditingController _queryController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _resultsController = ScrollController();
  
  bool _isLoading = false;
  // This field will be used when implementing schema loading
  // ignore: unused_field
  bool _isSchemaLoading = false;
  List<Map<String, dynamic>> _queryResults = [];
  List<Map<String, dynamic>> _schemas = [];
  List<Map<String, dynamic>> _tables = [];
  bool _showSchemaList = false;
  String? _selectedSchema;
  // This field will be used when implementing table selection
  // ignore: unused_field
  String? _selectedTable;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_filterTables);
    
    // Initialize the database service and fetch schemas when the page loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchSchemas();
    });
  }

  @override
  void dispose() {
    _queryController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _executeQuery() async {
    final query = _queryController.text.trim();
    if (query.isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Replace with your actual API endpoint
      final response = await http.post(
        Uri.parse('YOUR_BACKEND_ENDPOINT/execute-query'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'query': query}),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _queryResults = List<Map<String, dynamic>>.from(data['results']);
        });
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error executing query: ${response.body}')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _fetchSchemas() async {
    print('Fetching schemas...');
    setState(() {
      _isSchemaLoading = true;
      _errorMessage = null;
    });

    try {
      final dbService = DatabaseService();
      print('Database service instance created');
      
      if (!dbService.isConfigured) {
        throw Exception('Database not configured. Please check your environment variables.');
      }
      
      print('Fetching schemas from API...');
      final schemas = await dbService.getSchemas();
      print('Received ${schemas.length} schemas');
      
      setState(() {
        _schemas = schemas;
        if (schemas.isNotEmpty) {
          _selectedSchema = schemas.first['schema_name'];
          print('Selected schema: $_selectedSchema');
        } else {
          print('No schemas found');
        }
      });
    } catch (e) {
      final error = 'Failed to load schemas: $e';
      print(error);
      setState(() {
        _errorMessage = error;
      });
    } finally {
      setState(() {
        _isSchemaLoading = false;
      });
    }
  }

  Future<void> _fetchTables(String schema) async {
    if (schema.isEmpty) return;
    print('Fetching tables for schema: $schema');
    
    setState(() {
      _isSchemaLoading = true;
      _selectedSchema = schema;
      _errorMessage = null;
    });

    try {
      final dbService = DatabaseService();
      print('Fetching tables from API...');
      final tables = await dbService.getTables(schema);
      print('Received ${tables.length} tables');
      
      setState(() {
        _tables = tables;
      });
    } catch (e) {
      final error = 'Failed to load tables: $e';
      print(error);
      setState(() {
        _errorMessage = error;
      });
    } finally {
      setState(() {
        _isSchemaLoading = false;
      });
    }
  }

  void _filterTables() {
    // Implement search/filter logic if needed
  }

  Future<void> _insertTableSelect(String tableName) async {
    if (_selectedSchema == null) {
      final error = 'No schema selected';
      print(error);
      setState(() {
        _errorMessage = error;
      });
      return;
    }
    
    print('Loading columns for table: $_selectedSchema.$tableName');
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final dbService = DatabaseService();
      print('Fetching columns from API...');
      final columns = await dbService.getTableColumns(_selectedSchema!, tableName);
      print('Received ${columns.length} columns');
      
      if (columns.isEmpty) {
        throw Exception('No columns found for table $tableName');
      }
      
      final columnList = columns.map((c) => '"${c['column_name']}"').join(', ');
      final query = 'SELECT $columnList FROM "$_selectedSchema"."$tableName" LIMIT 100;';
      
      print('Generated query: $query');
      _queryController.text = query;
    } catch (e) {
      final error = 'Failed to load table columns: $e';
      print(error);
      setState(() {
        _errorMessage = error;
      });
    } finally {
      setState(() {
        _isLoading = false;
        _showSchemaList = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SQL Editor'),
        actions: [
          IconButton(
            icon: const Icon(Icons.play_arrow),
            onPressed: _isLoading ? null : _executeQuery,
            tooltip: 'Execute (Ctrl+Enter)',
          ),
        ],
      ),
      body: Column(
        children: [
          if (_errorMessage != null)
            Container(
              padding: const EdgeInsets.all(8.0),
              color: Colors.red[100],
              child: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.red),
                  const SizedBox(width: 8),
                  Expanded(child: Text(_errorMessage!)),
                  IconButton(
                    icon: const Icon(Icons.close, size: 18),
                    onPressed: () {
                      setState(() {
                        _errorMessage = null;
                      });
                    },
                  ),
                ],
              ),
            ),
          // Explore Tables Section
          ExpansionTile(
            title: const Text('Explore Tables'),
            initiallyExpanded: false,
            onExpansionChanged: (expanded) {
              if (expanded && _schemas.isEmpty) {
                _fetchSchemas();
              }
            },
            children: [
              if (_showSchemaList)
                Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TextField(
                        controller: _searchController,
                        decoration: const InputDecoration(
                          hintText: 'Search tables...',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(),
                        ),
                      ),
                    ),
                    // Schema List
                    if (_schemas.isNotEmpty)
                      ListView.builder(
                        shrinkWrap: true,
                        itemCount: _schemas.length,
                        itemBuilder: (context, index) {
                          final schema = _schemas[index];
                          return ExpansionTile(
                            title: Text(schema['schema_name'] ?? ''),
                            onExpansionChanged: (expanded) {
                              if (expanded) {
                                _fetchTables(schema['schema_name']);
                              }
                            },
                            children: [
                              if (_tables.isNotEmpty && _selectedSchema == schema['schema_name'])
                                ListView.builder(
                                  shrinkWrap: true,
                                  itemCount: _tables.length,
                                  itemBuilder: (context, index) {
                                    final table = _tables[index];
                                    return ListTile(
                                      title: Text(table['table_name'] ?? ''),
                                      onTap: () => _insertTableSelect(table['table_name']),
                                    );
                                  },
                                ),
                            ],
                          );
                        },
                      ),
                  ],
                ),
            ],
          ),
          // SQL Editor
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Stack(
                children: [
                  TextField(
                    controller: _queryController,
                    maxLines: null,
                    expands: true,
                    decoration: InputDecoration(
                      hintText: 'Enter your SQL query here...',
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.all(8.0),
                      suffixIcon: IconButton(
                        icon: const Icon(Icons.content_copy),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: _queryController.text));
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Query copied to clipboard')),
                          );
                        },
                        tooltip: 'Copy to clipboard',
                      ),
                    ),
                    style: const TextStyle(fontFamily: 'monospace', fontSize: 14),
                  ),
                  if (_isLoading)
                    const Center(child: CircularProgressIndicator()),
                ],
              ),
            ),
          ),
          // Query Results
          if (_queryResults.isNotEmpty)
            Expanded(
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Results (${_queryResults.length} rows)',
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                        IconButton(
                          icon: const Icon(Icons.content_copy, size: 18),
                          onPressed: () {
                            final csv = const ListToCsvConverter().convert([
                              _queryResults.first.keys.toList(),
                              ..._queryResults.map((row) => _queryResults.first.keys.map((key) => row[key]).toList())
                            ]);
                            Clipboard.setData(ClipboardData(text: csv));
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Results copied to clipboard')),
                            );
                          },
                          tooltip: 'Copy results as CSV',
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      controller: _resultsController,
                      scrollDirection: Axis.horizontal,
                      child: SingleChildScrollView(
                        child: DataTable(
                          columns: _queryResults.isNotEmpty
                              ? _queryResults.first.keys
                                  .map((key) => DataColumn(
                                        label: Text(
                                          key,
                                          style: const TextStyle(fontWeight: FontWeight.bold),
                                        ),
                                      ))
                                  .toList()
                              : [],
                          rows: _queryResults
                              .map((row) => DataRow(
                                    cells: row.entries
                                        .map((entry) => DataCell(
                                              Text(
                                                entry.value?.toString() ?? 'NULL',
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ))
                                        .toList(),
                                  ))
                              .toList(),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _isLoading ? null : _executeQuery,
        tooltip: 'Execute Query (Ctrl+Enter)',
        icon: _isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              )
            : const Icon(Icons.play_arrow),
        label: const Text('Execute'),
      ),
    );
  }
}
