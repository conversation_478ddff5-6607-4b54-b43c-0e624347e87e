import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import '../services/api_service.dart';
import '../widgets/request_list_item.dart';

class StoreVerificationRequestsScreen extends StatefulWidget {
  const StoreVerificationRequestsScreen({Key? key}) : super(key: key);

  @override
  State<StoreVerificationRequestsScreen> createState() =>
      _StoreVerificationRequestsScreenState();
}

class _StoreVerificationRequestsScreenState
    extends State<StoreVerificationRequestsScreen> {
  bool _isLoading = true;
  List<dynamic> _requests = [];
  String _errorMessage = '';
  final _scaffoldKey = GlobalKey<ScaffoldMessengerState>();

  @override
  void initState() {
    super.initState();
    _loadRequests();
  }

  Future<void> _loadRequests() async {
    if (!mounted) return;
    
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      print('Loading store verification requests...');
      final apiService = Provider.of<ApiService>(context, listen: false);
      final response = await apiService.getStoreVerificationRequests();
      
      if (mounted) {
        print('Store verification requests loaded: ${response.length} items');
        
        // Validate and filter response
        final validRequests = (response as List).where((item) {
          final storeHandle = item['storehandle']?.toString().trim();
          return storeHandle != null && storeHandle.isNotEmpty;
        }).toList();
        
        print('Valid store verification requests: ${validRequests.length}');
        
        // Sort by requested time (newest first)
        validRequests.sort((a, b) {
          final aTime = a['verification_requested_time']?.toString() ?? '';
          final bTime = b['verification_requested_time']?.toString() ?? '';
          return bTime.compareTo(aTime);
        });
        
        setState(() {
          _requests = validRequests;
          _isLoading = false;
        });
      }
    } on DioException catch (e) {
      final errorMessage = e.response?.data?.toString() ?? e.message ?? 'Unknown error';
      print('Error loading store verification requests: $errorMessage');
      print('Error details: $e');
      
      if (mounted) {
        setState(() {
          _errorMessage = 'Error: ${e.message ?? 'Failed to load store verification requests'}';
          if (e.response?.statusCode == 401) {
            _errorMessage = 'Session expired. Please login again.';
            // Optionally handle logout here
          }
          _isLoading = false;
        });
      }
    } catch (e, stackTrace) {
      print('Unexpected error: $e');
      print('Stack trace: $stackTrace');
      
      if (mounted) {
        setState(() {
          _errorMessage = 'An unexpected error occurred';
          _isLoading = false;
        });
      }
    }
  }

  // Show a snackbar message
  void _showMessage(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // Copy text to clipboard
  Future<void> _copyToClipboard(String text, String message) async {
    await Clipboard.setData(ClipboardData(text: text));
    _showMessage('$message copied to clipboard');
  }

  // Show rejection dialog
  Future<String?> _showRejectionDialog(BuildContext context, String verificationType) async {
    final controller = TextEditingController();
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Reject $verificationType Verification'),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            labelText: 'Reason for rejection',
            hintText: 'Enter the reason for rejecting $verificationType verification',
            border: const OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('CANCEL'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, controller.text.trim()),
            child: const Text('SUBMIT'),
          ),
        ],
      ),
    );
  }

  // Handle verification action
  Future<void> _handleVerificationAction(
    String storeHandle,
    String verificationType,
    bool isApproving,
  ) async {
    try {
      String? rejectionNotes;
      if (!isApproving) {
        final notes = await _showRejectionDialog(context, verificationType);
        if (notes == null || notes.isEmpty) {
          setState(() => _isLoading = false);
          return;
        }
        rejectionNotes = notes;
      }

      if (mounted) {
        setState(() {
          _isLoading = true;
        });
      }

      final apiService = Provider.of<ApiService>(context, listen: false);
      print('Sending verification update for $storeHandle: $verificationType = $isApproving');
      
      final result = await apiService.updateStoreVerification(
        storeHandle: storeHandle,
        verificationIdentity: verificationType,
        value: isApproving,
        rejectionNotes: rejectionNotes,
      );

      print('Verification update result: $result');

      if (result['success'] == true) {
        _showMessage('$verificationType verification ${isApproving ? 'approved' : 'rejected'} successfully');
        await _loadRequests(); // Refresh the list
      } else {
        _showMessage(result['error'] ?? 'Failed to update verification', isError: true);
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      _showMessage('An error occurred: $e', isError: true);
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Build action buttons for verification
  Widget _buildActionButtons(String storeHandle, List<String> verificationTypes) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: verificationTypes.map((type) {
        return Row(
          children: [
            const SizedBox(width: 4),
            ElevatedButton(
              onPressed: () => _handleVerificationAction(storeHandle, type, true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: const Size(0, 30),
              ),
              child: Text('Approve $type', style: const TextStyle(fontSize: 12)),
            ),
            const SizedBox(width: 4),
            OutlinedButton(
              onPressed: () => _handleVerificationAction(storeHandle, type, false),
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: Colors.red),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: const Size(0, 30),
              ),
              child: Text('Reject $type', style: const TextStyle(fontSize: 12, color: Colors.red)),
            ),
            const SizedBox(width: 4),
          ],
        );
      }).toList(),
    );
  }

  // Build copyable text with icon
  Widget _buildCopyableText(String label, String? value) {
    if (value == null) return const SizedBox.shrink();
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        children: [
          Text('$label: ', style: const TextStyle(fontWeight: FontWeight.bold)),
          Expanded(
            child: Text(
              value,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(fontFamily: 'monospace'),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.copy, size: 16),
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            onPressed: () => _copyToClipboard(value, label),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ScaffoldMessenger(
      key: _scaffoldKey,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Store Verification Requests'),
          centerTitle: true,
        ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _errorMessage,
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadRequests,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : _requests.isEmpty
                  ? Center(
                      child: Text(
                        'No store verification requests found',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _requests.length,
                      itemBuilder: (context, index) {
                        final request = _requests[index];
                        final storeHandle = (request['storehandle']?.toString().trim() ?? 'N/A').toUpperCase();
                        final verificationType = (request['verification_type']?.toString() ?? 'UNKNOWN').toUpperCase();
                        final idVerificationRequested = List<String>.from(
                          (request['ID_verification_requested'] as List<dynamic>?)?.map((e) => e.toString()) ?? []
                        );
                        
                        // Get PAN and GST details
                        final panNumber = request['pan_number']?.toString().trim() ?? 'Not provided';
                        final panName = request['pan_name']?.toString().trim() ?? 'Not provided';
                        final gstNumber = request['gst_number']?.toString().trim() ?? 'Not provided';
                        final gstBusinessName = request['gst_business_name']?.toString().trim() ?? 'Not provided';
                        
                        // Create a readable verification type string
                        String verificationTypeString = '';
                        if (idVerificationRequested.contains('PAN') && idVerificationRequested.contains('GST')) {
                          verificationTypeString = 'BUSINESS (PAN & GST)';
                        } else if (idVerificationRequested.contains('PAN')) {
                          verificationTypeString = 'INDIVIDUAL (PAN)';
                        } else if (idVerificationRequested.contains('GST')) {
                          verificationTypeString = 'BUSINESS (GST)';
                        } else {
                          verificationTypeString = 'VERIFICATION';
                        }
                        
                        // Create title with store handle and verification type
                        final title = '$storeHandle • $verificationTypeString';
                        
                        // Get verification types that need action
                        final needsVerification = <String>[];
                        if (request['ID_verification_requested'] != null) {
                          final verifications = List<String>.from(request['ID_verification_requested']);
                          for (var type in verifications) {
                            final isVerified = type == 'PAN' 
                                ? request['is_pan_verified'] ?? false
                                : request['is_gst_verified'] ?? false;
                            if (!isVerified) {
                              needsVerification.add(type);
                            }
                          }
                        }
                        
                        return Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Header with store handle and verification type
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        title,
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ),
                                    if (request['verification_requested_time'] != null)
                                      Text(
                                        request['verification_requested_time'].toString().split('T')[0],
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                
                                // PAN Details
                                _buildCopyableText('PAN Number', panNumber),
                                if (panName != 'Not provided')
                                  _buildCopyableText('PAN Name', panName),
                                
                                // GST Details
                                if (gstNumber != 'Not provided') ...[
                                  _buildCopyableText('GST Number', gstNumber),
                                  if (gstBusinessName != 'Not provided')
                                    _buildCopyableText('Business Name', gstBusinessName),
                                ],
                                
                                // Verification Actions
                                if (needsVerification.isNotEmpty) ...[
                                  const SizedBox(height: 8),
                                  Text(
                                    'Pending Verifications:',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.orange[800],
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: _buildActionButtons(storeHandle, needsVerification),
                                  ),
                                ] else ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    'All verifications completed',
                                    style: TextStyle(
                                      color: Colors.green[700],
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        );
                      },
                    ),
      ),
    );
  }
}
