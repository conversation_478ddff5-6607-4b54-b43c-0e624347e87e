# Resources
The below list of APIs are available to Swadesic Admin

# Server URL
> this is the base url for all the APIs.
DEV: https://e2e-77-175.ssdcloudindia.net/dev/
PROD: https://e2e-65-177.ssdcloudindia.net/prod

# To send O<PERSON> to selected few emails
> this api helps us to send O<PERSON> to selected few emails.

curl --location '{{baseUrl}}/swadesic_admin/send-otp/' \
--header 'Content-Type: application/json' \
--data-raw '{
    "email":"<EMAIL>"
}'

## response: 
{
    "message": "OTP sent successfully"
}


# Get auth session-token from email and OTP combination
curl --location '{{baseUrl}}/swadesic_admin/verify-otp/' \
--header 'Content-Type: application/json' \
--data-raw '{
    "email":"<EMAIL>",
    "otp":526842
}'

## response: 
{
    "message": "OTP verified successfully",
    "token": "eyJhbGciOiJIUzsdfsdfsdfInR5cCI6IkpXVCJ9.eyJyb2xlIsdfsdfsdfsdfsdfdsfX3VzZXIiLCJ1c2VyX2lkIjoxNDMsImV4cCI6MTc4NDkxODIxM30.sMorfOJWsdfsfsdfGpc2lLYUB9S6Vrqwn-2EusfsdfHmBk"
}

# get_store_verification_requests
> this api helps us to see the stores that have requested for verification and their details.

curl --location --globoff '{{baseUrl}}/swadesic_admin/get_store_verification_requests/' \
--header 'Authorization: {{Authother}}'

## response: 
[
    {
        "storehandle": "happy_birthday",
        "ID_verification_requested": [
            "PAN",
            "GST"
        ],
        "verification_type": "BUSINESS",
        "is_verification_completed": false,
        "first_verified_date": null,
        "is_pan_verified": false,
        "pan_number": "**********",
        "pan_name": "Repto ragnar",
        "pan_verified_time": null,
        "is_gst_verified": false,
        "gst_number": "22AAAAA0000A1Z5",
        "gst_business_name": "Happy birthday day",
        "gst_verified_time": null,
        "verification_requested_time": "2025-05-30T15:02:03.304749+05:30"
    },
    {
        "storehandle": "peekaboo_puppies",
        "ID_verification_requested": [
            "PAN"
        ],
        "verification_type": "INDIVIDUAL",
        "is_verification_completed": false,
        "first_verified_date": null,
        "is_pan_verified": false,
        "pan_number": "**********",
        "pan_name": "Ravi Kumar Krishna Kanth",
        "pan_verified_time": null,
        "is_gst_verified": false,
        "gst_number": null,
        "gst_business_name": null,
        "gst_verified_time": null,
        "verification_requested_time": "2025-05-29T14:46:05.767655+05:30"
    },
    {
        "storehandle": "lat_alphabets",
        "ID_verification_requested": [
            "PAN"
        ],
        "verification_type": "INDIVIDUAL",
        "is_verification_completed": false,
        "first_verified_date": null,
        "is_pan_verified": false,
        "pan_number": "**********",
        "pan_name": "Gangu bhai",
        "pan_verified_time": null,
        "is_gst_verified": false,
        "gst_number": null,
        "gst_business_name": null,
        "gst_verified_time": null,
        "verification_requested_time": "2025-05-28T12:16:57.894535+05:30"
    },
    {
        "storehandle": "xy",
        "ID_verification_requested": [
            "PAN"
        ],
        "verification_type": "INDIVIDUAL",
        "is_verification_completed": false,
        "first_verified_date": null,
        "is_pan_verified": false,
        "pan_number": "**********",
        "pan_name": "Xx",
        "pan_verified_time": null,
        "is_gst_verified": false,
        "gst_number": null,
        "gst_business_name": null,
        "gst_verified_time": null,
        "verification_requested_time": "2024-12-06T00:57:43.581612+05:30"
    },
    {
        "storehandle": "store_45",
        "ID_verification_requested": [
            "PAN"
        ],
        "verification_type": "INDIVIDUAL",
        "is_verification_completed": false,
        "first_verified_date": null,
        "is_pan_verified": false,
        "pan_number": "**********",
        "pan_name": "Hbhhh",
        "pan_verified_time": null,
        "is_gst_verified": false,
        "gst_number": null,
        "gst_business_name": null,
        "gst_verified_time": null,
        "verification_requested_time": "2024-10-02T03:33:21.867538+05:30"
    },
    {
        "storehandle": "test_store_45y",
        "ID_verification_requested": [
            "PAN"
        ],
        "verification_type": "INDIVIDUAL",
        "is_verification_completed": false,
        "first_verified_date": null,
        "is_pan_verified": false,
        "pan_number": "**********",
        "pan_name": "Ghhgg",
        "pan_verified_time": null,
        "is_gst_verified": false,
        "gst_number": null,
        "gst_business_name": null,
        "gst_verified_time": null,
        "verification_requested_time": "2024-10-02T02:15:04.496396+05:30"
    },
    {
        "storehandle": "ghgg_gh",
        "ID_verification_requested": [
            "PAN"
        ],
        "verification_type": "INDIVIDUAL",
        "is_verification_completed": false,
        "first_verified_date": null,
        "is_pan_verified": false,
        "pan_number": "**********",
        "pan_name": "Ghghh",
        "pan_verified_time": null,
        "is_gst_verified": false,
        "gst_number": null,
        "gst_business_name": null,
        "gst_verified_time": null,
        "verification_requested_time": "2024-10-01T15:53:09.752944+05:30"
    },
    {
        "storehandle": "repto_biryani",
        "ID_verification_requested": [
            "PAN"
        ],
        "verification_type": "INDIVIDUAL",
        "is_verification_completed": false,
        "first_verified_date": null,
        "is_pan_verified": false,
        "pan_number": "**********",
        "pan_name": "Ravi Kumar Krishna Kanth",
        "pan_verified_time": null,
        "is_gst_verified": false,
        "gst_number": null,
        "gst_business_name": null,
        "gst_verified_time": null,
        "verification_requested_time": "2024-10-01T11:13:24.692606+05:30"
    },
    {
        "storehandle": "mohit_34",
        "ID_verification_requested": [
            "PAN"
        ],
        "verification_type": "INDIVIDUAL",
        "is_verification_completed": false,
        "first_verified_date": null,
        "is_pan_verified": false,
        "pan_number": "**********",
        "pan_name": "Vbbb",
        "pan_verified_time": null,
        "is_gst_verified": false,
        "gst_number": null,
        "gst_business_name": null,
        "gst_verified_time": null,
        "verification_requested_time": "2024-10-01T11:08:16.014613+05:30"
    }
]


# update_store_verification requests
> this api helps us to approve or reject the verification request of received store verification requests.

curl --location --globoff --request PATCH '{{baseUrl}}/swadesic_admin/update_store_verification/{storehandle}/' \
--header 'Authorization: {{Authother}}' \
--form 'verification_identity="PAN"' \
--form 'value="TRUE"' \
--form 'rejection_notes="wrong pan details"'

> rejection_notes only when value = False

## response: 
{
    "storehandle": "priya_cosmetics",
    "ID_verification_requested": [],
    "verification_type": "BUSINESS",
    "is_verification_completed": true,
    "first_verified_date": "2024-09-14T14:10:42.158642+05:30",
    "is_pan_verified": true,
    "pan_number": "**********",
    "pan_name": "Krishna Priya",
    "pan_verified_time": "2024-10-10T13:12:52.190984+05:30",
    "is_gst_verified": true,
    "gst_number": "22AAAAA0000A1Z5",
    "gst_business_name": "Priya Cosmetics",
    "gst_verified_time": "2024-09-17T17:17:44.814990+05:30",
    "verification_requested_time": "2024-09-17T17:17:03.726314+05:30"
}



# get_pending_withdrawal_requests
> this api helps us to see the stores that have requested for withdrawal and their details.

curl --location --globoff '{{baseUrl}}/swadesic_admin/get_pending_withdrawal_requests/' \
--header 'Authorization: {{Auth}}' \
--header 'Content-Type: application/json' \

## response: 
[
    {
        "storehandle": "kk_cars",
        "store_reference": "S1729670649988",
        "transaction_reference": "**********",
        "transaction_type": "WITHDRAWAL",
        "transaction_status": "PENDING",
        "payout_amount": 100,
        "current_account_balance": 14312,
        "payout_release_date": "2024-10-25",
        "bank_account_number": "***************",
        "bank_account_name": "Union Bank of India",
        "bank_ifsc_code": "UBI0**********",
        "bank_name": "Union Bank of India",
        "bank_branch": null
    }
]

# complete_withdrawal_request
> this api helps us to complete the pending withdrawal request.

curl --location --globoff '{{baseUrl}}/swadesic_admin/complete_withdrawal_request/' \
--header 'Authorization: {{Auth}}' \
--header 'Content-Type: application/json' \
--form 'transaction_reference="**********"' \
--form 'status="FAILURE"' \
--form 'bank_reference_number="**********"' \
--form 'transaction_time="**********"' \
--form 'notes="Your Account balance amount got reduced. please try again after some successful orders"'


## response: 
{
    "message": "Withdrawal request updated successfully"
}


# create_subscription_plan: for user/store with monthly/yearly plans
> this api helps us to create a subscription plan for user/store with monthly/yearly plans.

curl --location 'http://***********:8000/swadesic_admin/subscription-plans/create/' \
--header 'Content-Type: application/json' \
--header 'Authorization: {{Auth}}' \
--data '{
    "plan_name": "Premium",
    "plan_description": "Enhanced features and priority support for power users",
    "plan_details": "• Everything in Free plan\n• Priority Support: Access to priority customer support for faster resolution\n• Receive 20% of Swadesic Order revenue as Affiliate Payout without order eligibility\n• Early access to Swadesic'\''s latest features",
    "plan_period": "monthly" or "yearly",
    "plan_amount": 299.00,
    "plan_type": "user" or "store"
}'

## response: 
{
    "message": "Subscription plan created successfully",
    "plan_reference": "PLAN1731623917828",
    "razorpay_plan_id": "plan_PLLvCUgIdvMxCd"
}



# get_subscription_invoice
> this api helps us to see if the user has paid for a month and what's missed, when he started all that. 

curl --location 'http://***********:8000/swadesic_admin/subscription/invoices/?subscription_id=sub_PNcdSfk4GCvnP1' \
--header 'Content-Type: application/json' \
--header 'Authorization: {{Auth}}' \

## response: 
{
    "message": "Invoices fetched successfully",
    "subscription_id": "sub_PNcdSfk4GCvnP1",
    "entity_type": "user",
    "entity_reference": "U1729670384908",
    "total_count": 1,
    "invoices": [
        {
            "invoice_id": "inv_PNcdTCHpNndgFf",
            "payment_id": "pay_PNclrOqXQ59RCL",
            "status": "paid",
            "amount": 299,
            "amount_paid": 299,
            "amount_due": 0,
            "currency": "INR",
            "issued_at": "2024-11-20T21:47:29",
            "paid_at": "2024-11-20T21:55:29",
            "cancelled_at": null,
            "expired_at": null,
            "billing_start": "2024-11-20T21:55:27",
            "billing_end": "2024-12-20T00:00:00",
            "short_url": "https://rzp.io/rzp/tvY3g3K",
            "customer_details": {
                "name": null,
                "email": "",
                "contact": "+919177858032"
            },
            "line_items": [
                {
                    "name": "Premium",
                    "amount": 299,
                    "currency": "INR",
                    "description": "Enhanced features and priority support for power users",
                    "type": "plan"
                }
            ]
        }
    ]
}