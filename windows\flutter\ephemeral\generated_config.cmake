# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\SRC\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\C-copy\\Projects\\GIT\\swadesic_admin_flutter" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\SRC\\flutter"
  "PROJECT_DIR=D:\\C-copy\\Projects\\GIT\\swadesic_admin_flutter"
  "FLUTTER_ROOT=D:\\SRC\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\C-copy\\Projects\\GIT\\swadesic_admin_flutter\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\C-copy\\Projects\\GIT\\swadesic_admin_flutter"
  "FLUTTER_TARGET=D:\\C-copy\\Projects\\GIT\\swadesic_admin_flutter\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC9jZjdhOWQwODAwZjJhNWRhMTY2ZGJlMGViOWZiMjQ3NjAxODI2OWIxLw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\C-copy\\Projects\\GIT\\swadesic_admin_flutter\\.dart_tool\\package_config.json"
)
