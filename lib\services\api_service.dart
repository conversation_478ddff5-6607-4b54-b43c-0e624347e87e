import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';
import 'auth_service.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class ApiService {
  final Dio _dio;
  final String _baseUrl = 'https://e2e-77-175.ssdcloudindia.net/dev';

  // Use the Dio instance from AuthService
  ApiService(Dio dio) : _dio = dio;

  // Helper method to generate curl command from request options
  static String _buildCurlCommand(RequestOptions options, {String? token}) {
    var curl = 'curl -X ${options.method} \\\n';

    // Add headers
    final headers = Map<String, dynamic>.from(options.headers);
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    headers.forEach((key, value) {
      if (key != 'content-length' && key != 'host' && key != 'user-agent') {
        curl += '  -H "$key: $value" \\\n';
      }
    });

    // Add URL
    curl += '  "${options.uri.toString()}" \\\n';

    // Add body if present
    if (options.data != null) {
      final body = options.data is Map || options.data is List
          ? options.data
          : options.data.toString();
      curl +=
          '  -d \'${body is String ? body : const JsonEncoder.withIndent('  ').convert(body)}\'';
    }

    return curl;
  }

  // Factory constructor to create ApiService with AuthService dependency
  static ApiService of(BuildContext context) {
    final authService = Provider.of<AuthService>(context, listen: false);

    // Use the Dio instance from AuthService which already has the auth interceptor
    final dio = authService.dio;

    // Add request logging interceptor
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        try {
          final token = await authService.getToken();

          // Log the curl command before making the request
          final curl = _buildCurlCommand(options, token: token);
          print('\n=== API Request ===');
          print('Endpoint: ${options.method} ${options.path}');
          print('CURL command:');
          print(curl);
          print('===================\n');

          return handler.next(options);
        } catch (e) {
          print('Error in request interceptor: $e');
          return handler.reject(DioException(
            requestOptions: options,
            error: 'Failed to prepare request: $e',
          ));
        }
      },
      onResponse: (response, handler) {
        print('\n=== API Response ===');
        print('Status: ${response.statusCode}');
        print('Method: ${response.requestOptions.method}');
        print('URL: ${response.requestOptions.uri}');
        print('Response:');
        print(response.data);
        print('===================\n');
        return handler.next(response);
      },
      onError: (error, handler) {
        if (error.response != null) {
          print('\n=== API Error ===');
          print('Status: ${error.response?.statusCode}');
          print('Method: ${error.requestOptions.method}');
          print('URL: ${error.requestOptions.uri}');
          print('Error:');
          print(error.response?.data);
          print('=================\n');
        } else {
          print('\n=== API Error ===');
          print('Error: ${error.message}');
          print('Type: ${error.type}');
          print('=================\n');
        }
        return handler.next(error);
      },
    ));

    return ApiService(dio);
  }

  // Get store verification requests
  Future<List<dynamic>> getStoreVerificationRequests() async {
    try {
      final url = '$_baseUrl/swadesic_admin/get_store_verification_requests/';
      print('Fetching store verification requests from: $url');

      final response = await _dio.get(
        url,
        options: Options(
          headers: await _getAuthHeaders(),
          validateStatus: (status) => status! < 500,
        ),
      );

      print('Response status: ${response.statusCode}');
      print('Response data: ${response.data}');

      if (response.statusCode == 200) {
        if (response.data is List) {
          print(
              'Successfully fetched ${response.data.length} store verification requests');
          return response.data;
        } else {
          print('Unexpected response format: ${response.data.runtimeType}');
          return [];
        }
      } else if (response.statusCode == 401) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          error: 'Authentication failed. Please login again.',
          type: DioExceptionType.badResponse,
        );
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          error: 'Failed to load store verification requests',
          type: DioExceptionType.badResponse,
        );
      }
    } on DioException catch (e) {
      print('DioException: ${e.message}');
      print('Error type: ${e.type}');
      print('Error response: ${e.response?.data}');
      rethrow;
    } catch (e) {
      print('Unexpected error: $e');
      rethrow;
    }
  }

  // Update store verification status
  Future<Map<String, dynamic>> updateStoreVerification({
    required String storeHandle,
    required String verificationIdentity,
    required bool value,
    String? rejectionNotes,
  }) async {
    try {
      final url =
          '$_baseUrl/swadesic_admin/update_store_verification/$storeHandle/';
      print(
          'Updating store verification for $storeHandle: $verificationIdentity = $value');

      final formData = FormData.fromMap({
        'verification_identity': verificationIdentity,
        'value': value.toString(),
        if (rejectionNotes != null && !value) 'rejection_notes': rejectionNotes,
      });

      final response = await _dio.patch(
        url,
        data: formData,
        options: Options(
          headers: await _getAuthHeaders(),
          validateStatus: (status) => status! < 500,
        ),
      );

      print('Response status: ${response.statusCode}');
      print('Response data: ${response.data}');

      if (response.statusCode == 200) {
        return {'success': true, 'data': response.data};
      } else {
        return {
          'success': false,
          'error': response.data?.toString() ?? 'Failed to update verification'
        };
      }
    } on DioException catch (e) {
      print('DioException: ${e.message}');
      print('Error type: ${e.type}');
      print('Error response: ${e.response?.data}');
      return {'success': false, 'error': e.message ?? 'Network error occurred'};
    } catch (e) {
      print('Unexpected error: $e');
      return {'success': false, 'error': 'An unexpected error occurred'};
    }
  }

  // Get pending withdrawal requests
  Future<List<dynamic>> getWithdrawalRequests() async {
    try {
      final url = '$_baseUrl/swadesic_admin/get_pending_withdrawal_requests/';
      print('Fetching withdrawal requests from: $url');

      final response = await _dio.get(
        url,
        options: Options(
          headers: await _getAuthHeaders(),
          validateStatus: (status) => status! < 500,
        ),
      );

      print('Response status: ${response.statusCode}');
      print('Response data: ${response.data}');

      if (response.statusCode == 200) {
        if (response.data is List) {
          print(
              'Successfully fetched ${response.data.length} withdrawal requests');
          return response.data;
        } else {
          print('Unexpected response format: ${response.data.runtimeType}');
          return [];
        }
      } else if (response.statusCode == 401) {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          error: 'Authentication failed. Please login again.',
          type: DioExceptionType.badResponse,
        );
      } else {
        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          error: 'Failed to load withdrawal requests',
          type: DioExceptionType.badResponse,
        );
      }
    } on DioException catch (e) {
      print('DioException: ${e.message}');
      print('Error type: ${e.type}');
      print('Error response: ${e.response?.data}');
      rethrow;
    } catch (e) {
      print('Unexpected error: $e');
      rethrow;
    }
  }

  // Helper method to get auth headers
  Future<Map<String, dynamic>> _getAuthHeaders() async {
    final token = await _getToken();
    return {
      'Authorization': 'Bearer $token',
      'Content-Type': 'application/json',
    };
  }

  // Helper method to get token
  Future<String?> _getToken() async {
    try {
      // Get the BuildContext from the navigator key
      final context = navigatorKey.currentContext;
      if (context == null) {
        print('Error: No BuildContext available');
        return null;
      }

      // Get the auth service
      final authService = Provider.of<AuthService>(
        context,
        listen: false,
      );

      // Get and return the token
      final token = await authService.getToken();
      if (token == null) {
        print('No auth token available');
      } else {
        print(
            'Retrieved auth token (first 5 chars): ${token.substring(0, token.length > 5 ? 5 : token.length)}...');
      }
      return token;
    } catch (e) {
      print('Error getting auth token: $e');
      return null;
    }
  }
}
